"use client"

import type React from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Card, CardContent } from "@/src/components/ui/card"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Textarea } from "@/src/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { MobileNav } from "@/src/components/mobile-nav"
import {
  Clock,
  Github,
  Linkedin,
  Mail,
  MapPin,
  Send,
  MessageCircle,
  Coffee,
  CheckCircle,
  ExternalLink,
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { useRef } from "react"

export default function Contact() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
    },
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast({
      title: "Message sent successfully!",
      description: "Thank you for reaching out. I'll get back to you within 24 hours.",
    })

    setFormData({
      name: "",
      email: "",
      subject: "",
      message: "",
    })
    setIsSubmitting(false)
  }

  const isFormValid = formData.name && formData.email && formData.subject && formData.message

  const contactMethods = [
    {
      icon: Mail,
      title: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      description: "Best for detailed inquiries",
      color: "from-red-500 to-pink-600",
    },
    {
      icon: Linkedin,
      title: "LinkedIn",
      value: "linkedin.com/in/cjjutba",
      href: "https://linkedin.com/in/cjjutba",
      description: "Professional networking",
      color: "from-blue-500 to-blue-700",
    },
    {
      icon: Github,
      title: "GitHub",
      value: "github.com/cjjutba",
      href: "https://github.com/cjjutba",
      description: "View my code and projects",
      color: "from-gray-600 to-gray-800",
    },
  ]

  const availabilityInfo = [
    {
      icon: CheckCircle,
      title: "Available for new opportunities",
      description: "Open to frontend and full-stack positions",
      status: "positive",
    },
    {
      icon: Clock,
      title: "Response time: 24 hours",
      description: "Usually respond within a few hours",
      status: "neutral",
    },
    {
      icon: MapPin,
      title: "Remote-friendly",
      description: "Open to remote and hybrid work",
      status: "positive",
    },
    {
      icon: Coffee,
      title: "Coffee chat ready",
      description: "Always up for a good conversation",
      status: "positive",
    },
  ]

  const lookingFor = [
    "Frontend Developer positions",
    "Full-stack development opportunities",
    "Freelance web development projects",
    "Open source collaboration",
    "Mentorship and learning opportunities",
    "Tech community connections",
  ]

  return (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], ["0%", "-30%"]) }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-emerald-400/10 to-blue-400/10 rounded-full blur-3xl"
        />
      </div>

      <div className="relative p-6 lg:p-8">
        <div className="flex items-center justify-between mb-8 lg:hidden">
          <h1 className="text-2xl font-bold">Contact</h1>
          <MobileNav />
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-6xl mx-auto space-y-20"
        >
          {/* Enhanced Header */}
          <motion.section variants={itemVariants} className="text-center space-y-8 py-12">
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="relative inline-block"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-2xl rounded-full" />
                <div className="relative px-6 py-3 rounded-full border border-blue-200/20 bg-white/5 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      Let's start a conversation
                    </span>
                  </div>
                </div>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.2 }}
                className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
              >
                Get In
                <br />
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent">
                  Touch
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              >
                I'd love to hear from you. Send me a message and
                <span className="font-semibold text-foreground"> I'll respond as soon as possible</span>
              </motion.p>
            </div>
          </motion.section>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <Card className="border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm shadow-xl">
                <CardContent className="p-8 lg:p-12">
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <h2 className="text-3xl font-bold">Send me a message</h2>
                      <p className="text-muted-foreground">
                        Fill out the form below and I'll get back to you within 24 hours.
                      </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-sm font-medium">
                            Name *
                          </Label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                            required
                            className="h-12 bg-white/50 dark:bg-gray-900/50 border-gray-200/50 dark:border-gray-700/50 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-sm font-medium">
                            Email *
                          </Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                            required
                            className="h-12 bg-white/50 dark:bg-gray-900/50 border-gray-200/50 dark:border-gray-700/50 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subject" className="text-sm font-medium">
                          Subject *
                        </Label>
                        <Input
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="What's this about?"
                          required
                          className="h-12 bg-white/50 dark:bg-gray-900/50 border-gray-200/50 dark:border-gray-700/50 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message" className="text-sm font-medium">
                          Message *
                        </Label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          placeholder="Tell me more about your project or inquiry..."
                          rows={6}
                          required
                          className="bg-white/50 dark:bg-gray-900/50 border-gray-200/50 dark:border-gray-700/50 focus:border-blue-500 dark:focus:border-blue-400 transition-colors resize-none"
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-14 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
                        size="lg"
                        disabled={!isFormValid || isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="mr-2 h-4 w-4" />
                            Send Message
                          </>
                        )}
                      </Button>
                    </form>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-6">
              {/* Contact Methods */}
              <Card className="border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm shadow-xl">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Contact Information</h3>
                      <p className="text-muted-foreground text-sm">Other ways to reach me</p>
                    </div>

                    <div className="space-y-4">
                      {contactMethods.map((method, index) => (
                        <motion.div
                          key={method.title}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                        >
                          <Link
                            href={method.href}
                            target={method.href.startsWith("http") ? "_blank" : undefined}
                            rel={method.href.startsWith("http") ? "noopener noreferrer" : undefined}
                            className="group flex items-center space-x-4 p-4 rounded-xl hover:bg-white/50 dark:hover:bg-gray-900/50 transition-all duration-300 hover:scale-[1.02]"
                          >
                            <div
                              className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br ${method.color} group-hover:scale-110 transition-transform duration-300`}
                            >
                              <method.icon className="h-5 w-5 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-semibold group-hover:text-blue-600 transition-colors">
                                {method.title}
                              </div>
                              <div className="text-xs text-muted-foreground truncate">{method.value}</div>
                              <div className="text-xs text-muted-foreground mt-1">{method.description}</div>
                            </div>
                            <ExternalLink className="h-4 w-4 text-muted-foreground group-hover:text-blue-600 transition-colors" />
                          </Link>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Availability */}
              <Card className="border-0 bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-950/30 dark:to-emerald-950/30 backdrop-blur-sm shadow-xl">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Availability</h3>
                      <p className="text-muted-foreground text-sm">Current status and response times</p>
                    </div>

                    <div className="space-y-4">
                      {availabilityInfo.map((info, index) => (
                        <motion.div
                          key={info.title}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.6 + index * 0.1, duration: 0.6 }}
                          className="flex items-start space-x-3"
                        >
                          <div
                            className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${
                              info.status === "positive"
                                ? "bg-green-100 dark:bg-green-900/30"
                                : "bg-blue-100 dark:bg-blue-900/30"
                            }`}
                          >
                            <info.icon
                              className={`w-4 h-4 ${
                                info.status === "positive"
                                  ? "text-green-600 dark:text-green-400"
                                  : "text-blue-600 dark:text-blue-400"
                              }`}
                            />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{info.title}</div>
                            <div className="text-xs text-muted-foreground">{info.description}</div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* What I'm Looking For */}
              <Card className="border-0 bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/30 dark:to-pink-950/30 backdrop-blur-sm shadow-xl">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-2">What I'm Looking For</h3>
                      <p className="text-muted-foreground text-sm">Opportunities and collaborations</p>
                    </div>

                    <ul className="space-y-3">
                      {lookingFor.map((item, index) => (
                        <motion.li
                          key={item}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 2.0 + index * 0.1, duration: 0.6 }}
                          className="flex items-start space-x-3 text-sm"
                        >
                          <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{item}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
