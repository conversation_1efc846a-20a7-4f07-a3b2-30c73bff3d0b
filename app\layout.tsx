import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON>, JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/src/components/theme-provider"
import { Sidebar } from "@/src/components/sidebar"
import { FloatingNav } from "@/src/components/floating-nav"
import { Toaster } from "@/src/components/ui/toaster"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains-mono",
  display: "swap",
})

export const metadata: Metadata = {
  title: "CJ <PERSON> - Frontend Developer",
  description:
    "Personal portfolio of <PERSON><PERSON>, a Computer Engineering graduate and Frontend Developer specializing in modern web development.",
  keywords: ["Frontend Developer", "Computer Engineering", "React", "Next.js", "TypeScript", "Web Development"],
  authors: [{ name: "<PERSON><PERSON>", url: "https://cjjutba.com" }],
  openGraph: {
    title: "C<PERSON> - Frontend Developer",
    description: "Personal portfolio showcasing modern web development projects and skills.",
    url: "https://cjjutba.com",
    siteName: "CJ Jutba Portfolio",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "CJ Jutba - Frontend Developer",
    description: "Personal portfolio showcasing modern web development projects and skills.",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${jetbrainsMono.variable} ${inter.className}`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="flex min-h-screen bg-background">
            <Sidebar />
            <main className="flex-1 lg:ml-80">
              <div className="relative">
                {children}
                <FloatingNav />
              </div>
            </main>
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
