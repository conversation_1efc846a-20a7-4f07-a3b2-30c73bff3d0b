"use client"

import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Card, CardContent } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { MobileNav } from "@/src/components/mobile-nav"
import {
  ArrowRight,
  ExternalLink,
  Github,
  MapPin,
  Zap,
  Code2,
  Sparkles,
  TrendingUp,
  Award,
  Users,
  Coffee,
} from "lucide-react"
import Link from "next/link"
import { motion, useScroll, useTransform } from "framer-motion"
import { useRef } from "react"

export default function Dashboard() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
    },
  }

  const skills = [
    { name: "React & Next.js", level: 90, category: "Frontend", color: "bg-blue-500" },
    { name: "TypeScript", level: 85, category: "Language", color: "bg-indigo-500" },
    { name: "Tailwind CSS", level: 95, category: "Styling", color: "bg-cyan-500" },
    { name: "Node.js", level: 70, category: "Backend", color: "bg-green-500" },
    { name: "UI/UX Design", level: 80, category: "Design", color: "bg-purple-500" },
  ]

  const achievements = [
    { icon: Code2, label: "Clean Code Advocate", description: "Maintainable, scalable solutions" },
    { icon: Zap, label: "Performance Focused", description: "Optimized user experiences" },
    { icon: Users, label: "Collaborative", description: "Team player & mentor" },
    { icon: Award, label: "Problem Solver", description: "Creative technical solutions" },
  ]

  return (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Animated background elements - Purple Tech Theme */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-tech-purple-400/10 to-tech-purple-600/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], ["0%", "-30%"]) }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-tech-purple-600/10 to-tech-purple-400/10 rounded-full blur-3xl"
        />
      </div>

      <div className="relative p-6 lg:p-8">
        <div className="flex items-center justify-between mb-8 lg:hidden">
          <h1 className="text-2xl font-bold">Portfolio</h1>
          <MobileNav />
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-6xl mx-auto space-y-20"
        >
          {/* Enhanced Hero Section */}
          <motion.section variants={itemVariants} className="relative py-20">
            <div className="text-center space-y-8">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="relative inline-block"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-tech-purple-600/20 to-tech-purple-400/20 blur-2xl rounded-full" />
                <div className="relative px-6 py-3 rounded-full border border-tech-purple-200/20 bg-white/5 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-tech-purple-600 dark:text-tech-purple-400">
                      Available for Frontend Opportunities
                    </span>
                    <MapPin className="w-4 h-4 text-tech-purple-600 dark:text-tech-purple-400" />
                  </div>
                </div>
              </motion.div>

              <div className="space-y-6">
                <motion.h1
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, delay: 0.2 }}
                  className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
                >
                  Crafting Digital
                  <br />
                  <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                    Experiences
                  </span>
                </motion.h1>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="max-w-3xl mx-auto space-y-4"
                >
                  <p className="text-xl lg:text-2xl text-muted-foreground leading-relaxed">
                    Computer Engineering graduate specializing in{" "}
                    <span className="font-semibold text-foreground">modern web applications</span> with a focus on{" "}
                    <span className="font-semibold text-foreground">performance</span> and{" "}
                    <span className="font-semibold text-foreground">user experience</span>
                  </p>

                  <div className="flex flex-wrap justify-center gap-3 pt-4">
                    {["React", "Next.js", "TypeScript", "Tailwind CSS"].map((tech, index) => (
                      <motion.div
                        key={tech}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Badge
                          variant="secondary"
                          className="px-4 py-2 text-sm font-medium bg-tech-purple-50 dark:bg-tech-purple-950/30 text-tech-purple-700 dark:text-tech-purple-300 border-tech-purple-200 dark:border-tech-purple-800 hover:bg-tech-purple-100 dark:hover:bg-tech-purple-950/50 transition-colors cursor-default"
                        >
                          {tech}
                        </Badge>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.section>

          {/* Skills Showcase */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">
                <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                  Technical Expertise
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Specialized skills developed through hands-on projects and continuous learning
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {skills.map((skill, index) => (
                <motion.div
                  key={skill.name}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                  whileHover={{ scale: 1.02 }}
                  className="group"
                >
                  <Card className="p-6 border-0 bg-gradient-to-br from-white/50 to-white/30 dark:from-tech-purple-900/20 dark:to-tech-purple-800/10 backdrop-blur-sm hover:shadow-xl transition-all duration-500 hover:shadow-tech-purple-500/20">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold group-hover:text-tech-purple-600 transition-colors">
                            {skill.name}
                          </h3>
                          <p className="text-sm text-muted-foreground">{skill.category}</p>
                        </div>
                        <div className="text-2xl font-bold text-tech-purple-600">{skill.level}%</div>
                      </div>

                      <div className="space-y-2">
                        <div className="w-full bg-gray-200 dark:bg-tech-purple-900/30 rounded-full h-2 overflow-hidden">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${skill.level}%` }}
                            transition={{ delay: 1.5 + index * 0.1, duration: 1.2, ease: "easeOut" }}
                            className="h-full bg-gradient-to-r from-tech-purple-600 to-tech-purple-500 rounded-full relative"
                          >
                            <div className="absolute inset-0 bg-white/20 animate-pulse" />
                          </motion.div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Achievements Grid */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">
                <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                  What I Bring
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Core values and approaches that drive my development process
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.label}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.8 + index * 0.1, duration: 0.6 }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="group"
                >
                  <Card className="p-6 text-center border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-tech-purple-900/20 dark:to-tech-purple-800/10 backdrop-blur-sm hover:shadow-xl transition-all duration-500 hover:shadow-tech-purple-500/20">
                    <div className="space-y-4">
                      <div className="mx-auto w-12 h-12 bg-gradient-to-br from-tech-purple-600 to-tech-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <achievement.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg group-hover:text-tech-purple-600 transition-colors">
                          {achievement.label}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-2">{achievement.description}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Current Focus */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-tech-purple-50/80 to-tech-purple-100/60 dark:from-tech-purple-950/30 dark:to-tech-purple-900/20 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-tech-purple-600/5 to-tech-purple-500/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto text-center space-y-8">
                  <div className="space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-tech-purple-100 dark:bg-tech-purple-900/30 text-tech-purple-700 dark:text-tech-purple-300">
                      <TrendingUp className="w-4 h-4" />
                      <span className="text-sm font-medium">Currently Expanding</span>
                    </div>

                    <h2 className="text-3xl lg:text-4xl font-bold">
                      <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                        Diving Deeper Into Full-Stack Development
                      </span>
                    </h2>

                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                      Expanding my expertise beyond frontend to become a well-rounded developer capable of building
                      complete web applications from concept to deployment.
                    </p>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    {[
                      { name: "Node.js", progress: 70 },
                      { name: "PostgreSQL", progress: 60 },
                      { name: "Docker", progress: 45 },
                      { name: "AWS", progress: 40 },
                      { name: "GraphQL", progress: 55 },
                    ].map((tech, index) => (
                      <motion.div
                        key={tech.name}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 2.2 + index * 0.1, duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        className="p-4 rounded-xl bg-white/60 dark:bg-tech-purple-900/20 backdrop-blur-sm border border-white/20 dark:border-tech-purple-500/20 hover:shadow-lg hover:shadow-tech-purple-500/10 transition-all duration-300"
                      >
                        <div className="space-y-3">
                          <h4 className="font-medium text-sm">{tech.name}</h4>
                          <div className="w-full bg-gray-200 dark:bg-tech-purple-900/30 rounded-full h-1.5">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${tech.progress}%` }}
                              transition={{ delay: 2.5 + index * 0.1, duration: 1, ease: "easeOut" }}
                              className="h-full bg-gradient-to-r from-tech-purple-600 to-tech-purple-500 rounded-full"
                            />
                          </div>
                          <span className="text-xs text-muted-foreground">{tech.progress}%</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* Enhanced Call to Action */}
          <motion.section variants={itemVariants} className="text-center space-y-12 py-20">
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 2.8, duration: 0.8 }}
                className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 text-green-700 dark:text-green-300"
              >
                <Coffee className="w-4 h-4" />
                <span className="text-sm font-medium">Ready to collaborate</span>
              </motion.div>

              <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
                Let's Create Something
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Extraordinary
                </span>
              </h2>

              <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                I'm passionate about turning ideas into polished, user-friendly applications. Let's discuss how we can
                bring your vision to life.
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 3.2, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                asChild
                size="lg"
                className="h-14 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
              >
                <Link href="/projects" className="flex items-center">
                  <Sparkles className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
                  Explore My Work
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                asChild
                className="h-14 px-8 border-2 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-300 hover:scale-105 group bg-transparent"
              >
                <Link href="/contact" className="flex items-center">
                  Let's Connect
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                </Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 3.6, duration: 0.6 }}
              className="flex justify-center space-x-6 pt-8"
            >
              <Link
                href="https://github.com/cjjutba"
                target="_blank"
                className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors group"
              >
                <Github className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium">View Code</span>
              </Link>
              <Link
                href="https://linkedin.com/in/cjjutba"
                target="_blank"
                className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors group"
              >
                <Users className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium">Connect</span>
              </Link>
            </motion.div>
          </motion.section>
        </motion.div>
      </div>
    </div>
  )
}
