"use client"

import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Card, CardContent } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { MobileNav } from "@/src/components/mobile-nav"
import { ExternalLink, Github, Star, TrendingUp, <PERSON>2, <PERSON><PERSON>, <PERSON>ap, Users, Award, Rocket } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion, useScroll, useTransform } from "framer-motion"
import { useRef } from "react"

export default function Projects() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
    },
  }

  const projects = [
    {
      id: 1,
      title: "E-Commerce Dashboard",
      description:
        "A modern admin dashboard for managing e-commerce operations with real-time analytics, inventory management, and comprehensive order tracking system.",
      image: "/placeholder.svg?height=300&width=500&text=E-Commerce+Dashboard",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Chart.js", "PostgreSQL"],
      liveUrl: "#",
      githubUrl: "#",
      featured: true,
      category: "Full-Stack",
      status: "Completed",
      highlights: ["Real-time Analytics", "Inventory Management", "Order Tracking"],
    },
    {
      id: 2,
      title: "Task Management Platform",
      description:
        "A collaborative task management application with advanced drag-and-drop functionality, team collaboration features, and comprehensive progress tracking.",
      image: "/placeholder.svg?height=300&width=500&text=Task+Management+Platform",
      technologies: ["React", "Node.js", "MongoDB", "Socket.io", "Material-UI", "Express"],
      liveUrl: "#",
      githubUrl: "#",
      featured: true,
      category: "Full-Stack",
      status: "Completed",
      highlights: ["Real-time Collaboration", "Drag & Drop", "Team Management"],
    },
    {
      id: 3,
      title: "Weather Analytics App",
      description:
        "A comprehensive weather application with location-based forecasts, interactive maps, detailed weather analytics, and historical data visualization.",
      image: "/placeholder.svg?height=300&width=500&text=Weather+Analytics+App",
      technologies: ["React", "OpenWeather API", "D3.js", "CSS Modules", "Chart.js"],
      liveUrl: "#",
      githubUrl: "#",
      featured: true,
      category: "Frontend",
      status: "Completed",
      highlights: ["Interactive Maps", "Data Visualization", "Historical Analysis"],
    },
    {
      id: 4,
      title: "Portfolio Website",
      description:
        "A modern, responsive portfolio website showcasing projects and skills with sophisticated animations, smooth transitions, and optimized performance.",
      image: "/placeholder.svg?height=300&width=500&text=Portfolio+Website",
      technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Vercel"],
      liveUrl: "#",
      githubUrl: "#",
      featured: false,
      category: "Frontend",
      status: "Completed",
      highlights: ["Modern Design", "Smooth Animations", "Performance Optimized"],
    },
    {
      id: 5,
      title: "Recipe Discovery Platform",
      description:
        "An intelligent recipe discovery app with advanced search functionality, meal planning features, nutritional information, and personalized recommendations.",
      image: "/placeholder.svg?height=300&width=500&text=Recipe+Discovery+Platform",
      technologies: ["React", "Spoonacular API", "Styled Components", "React Router", "Firebase"],
      liveUrl: "#",
      githubUrl: "#",
      featured: false,
      category: "Frontend",
      status: "In Progress",
      highlights: ["Smart Search", "Meal Planning", "Nutrition Tracking"],
    },
    {
      id: 6,
      title: "Financial Tracker",
      description:
        "A comprehensive personal finance management app with expense categorization, budget tracking, financial insights visualization, and goal setting.",
      image: "/placeholder.svg?height=300&width=500&text=Financial+Tracker",
      technologies: ["React", "Firebase", "Chart.js", "Material-UI", "PWA"],
      liveUrl: "#",
      githubUrl: "#",
      featured: false,
      category: "Frontend",
      status: "Completed",
      highlights: ["Budget Tracking", "Financial Insights", "Goal Setting"],
    },
  ]

  const featuredProjects = projects.filter((project) => project.featured)
  const otherProjects = projects.filter((project) => !project.featured)

  const projectStats = [
    { icon: Code2, label: "Total Projects", value: "12+", color: "from-blue-500 to-cyan-600" },
    { icon: Star, label: "Featured Work", value: "3", color: "from-yellow-500 to-orange-600" },
    { icon: TrendingUp, label: "Technologies", value: "15+", color: "from-green-500 to-emerald-600" },
    { icon: Users, label: "Collaborations", value: "5+", color: "from-purple-500 to-pink-600" },
  ]

  return (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], ["0%", "-30%"]) }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-emerald-400/10 to-blue-400/10 rounded-full blur-3xl"
        />
      </div>

      <div className="relative p-6 lg:p-8">
        <div className="flex items-center justify-between mb-8 lg:hidden">
          <h1 className="text-2xl font-bold">Projects</h1>
          <MobileNav />
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-7xl mx-auto space-y-20"
        >
          {/* Enhanced Header */}
          <motion.section variants={itemVariants} className="text-center space-y-8 py-12">
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="relative inline-block"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-2xl rounded-full" />
                <div className="relative px-6 py-3 rounded-full border border-blue-200/20 bg-white/5 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <Rocket className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      Featured Work & Case Studies
                    </span>
                  </div>
                </div>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.2 }}
                className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
              >
                My
                <br />
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent">
                  Projects
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              >
                A showcase of my work and the
                <span className="font-semibold text-foreground"> technologies I've mastered</span> to build them
              </motion.p>
            </div>
          </motion.section>

          {/* Project Stats */}
          <motion.section variants={itemVariants} className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {projectStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                whileHover={{ scale: 1.05, y: -2 }}
                className="group"
              >
                <Card className="p-6 text-center border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-xl transition-all duration-500">
                  <div className="space-y-3">
                    <div
                      className={`mx-auto w-12 h-12 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-3xl font-bold group-hover:text-blue-600 transition-colors">{stat.value}</div>
                    <p className="text-sm text-muted-foreground font-medium">{stat.label}</p>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.section>

          {/* Featured Projects */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">Featured Projects</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Highlighted work that demonstrates my technical skills and problem-solving abilities
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {featuredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.8 + index * 0.2, duration: 0.6 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group"
                >
                  <Card className="overflow-hidden border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 h-full">
                    <div className="relative">
                      <div className="aspect-video relative overflow-hidden">
                        <Image
                          src={project.image || "/placeholder.svg"}
                          alt={project.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>
                      <div className="absolute top-4 left-4 flex gap-2">
                        <Badge className="bg-blue-500/90 text-white border-0 backdrop-blur-sm">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                        <Badge variant="secondary" className="bg-white/90 text-gray-800 border-0 backdrop-blur-sm">
                          {project.category}
                        </Badge>
                      </div>
                      <div className="absolute top-4 right-4">
                        <Badge
                          variant={project.status === "Completed" ? "default" : "secondary"}
                          className={`${project.status === "Completed" ? "bg-green-500/90" : "bg-orange-500/90"} text-white border-0 backdrop-blur-sm`}
                        >
                          {project.status}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold group-hover:text-blue-600 transition-colors mb-2">
                          {project.title}
                        </h3>
                        <p className="text-muted-foreground text-sm leading-relaxed">{project.description}</p>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <p className="text-xs font-medium text-muted-foreground mb-2">Key Features</p>
                          <div className="flex flex-wrap gap-1">
                            {project.highlights.map((highlight) => (
                              <Badge
                                key={highlight}
                                variant="outline"
                                className="text-xs bg-blue-50 dark:bg-blue-950/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                              >
                                {highlight}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <p className="text-xs font-medium text-muted-foreground mb-2">Technologies</p>
                          <div className="flex flex-wrap gap-1">
                            {project.technologies.slice(0, 4).map((tech) => (
                              <Badge key={tech} variant="outline" className="text-xs">
                                {tech}
                              </Badge>
                            ))}
                            {project.technologies.length > 4 && (
                              <Badge variant="outline" className="text-xs">
                                +{project.technologies.length - 4}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-3 pt-2">
                        <Button
                          size="sm"
                          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                          asChild
                        >
                          <Link href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="mr-2 h-3 w-3" />
                            Live Demo
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-900/50"
                          asChild
                        >
                          <Link href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                            <Github className="mr-2 h-3 w-3" />
                            Code
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Other Projects */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">Other Projects</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Additional work showcasing diverse skills and continuous learning
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {otherProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.4 + index * 0.1, duration: 0.6 }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="group"
                >
                  <Card className="overflow-hidden border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-xl transition-all duration-500 h-full">
                    <div className="relative">
                      <div className="aspect-video relative overflow-hidden">
                        <Image
                          src={project.image || "/placeholder.svg"}
                          alt={project.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary" className="bg-white/90 text-gray-800 border-0 backdrop-blur-sm">
                          {project.category}
                        </Badge>
                      </div>
                      <div className="absolute top-4 right-4">
                        <Badge
                          variant={project.status === "Completed" ? "default" : "secondary"}
                          className={`${project.status === "Completed" ? "bg-green-500/90" : "bg-orange-500/90"} text-white border-0 backdrop-blur-sm`}
                        >
                          {project.status}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6 space-y-4">
                      <div>
                        <h3 className="text-lg font-bold group-hover:text-blue-600 transition-colors mb-2">
                          {project.title}
                        </h3>
                        <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
                          {project.description}
                        </p>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <p className="text-xs font-medium text-muted-foreground mb-2">Technologies</p>
                          <div className="flex flex-wrap gap-1">
                            {project.technologies.slice(0, 3).map((tech) => (
                              <Badge key={tech} variant="outline" className="text-xs">
                                {tech}
                              </Badge>
                            ))}
                            {project.technologies.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{project.technologies.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-950/30"
                          asChild
                        >
                          <Link href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="mr-2 h-3 w-3" />
                            Demo
                          </Link>
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-900/50"
                          asChild
                        >
                          <Link href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                            <Github className="mr-2 h-3 w-3" />
                            Code
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Project Insights */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50/80 to-blue-50/80 dark:from-emerald-950/30 dark:to-blue-950/30 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/5 to-blue-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto space-y-8">
                  <div className="text-center space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300">
                      <Award className="w-4 h-4" />
                      <span className="text-sm font-medium">Development Insights</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">Project Insights</h2>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                      Technologies and patterns I frequently use in my projects
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center">
                        <Code2 className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-blue-600 mb-2">React</div>
                        <p className="text-sm text-muted-foreground">Most used framework</p>
                      </div>
                    </div>
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">
                        <Zap className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-purple-600 mb-2">TypeScript</div>
                        <p className="text-sm text-muted-foreground">Preferred language</p>
                      </div>
                    </div>
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center">
                        <Palette className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-emerald-600 mb-2">Responsive</div>
                        <p className="text-sm text-muted-foreground">Design approach</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* Call to Action */}
          <motion.section variants={itemVariants} className="text-center space-y-12 py-20">
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 3.2, duration: 0.8 }}
                className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300"
              >
                <Users className="w-4 h-4" />
                <span className="text-sm font-medium">Interested in collaborating?</span>
              </motion.div>

              <h2 className="text-4xl lg:text-5xl font-bold leading-tight">
                Let's Work Together on
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Your Next Project
                </span>
              </h2>

              <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                I'm always open to discussing new opportunities and interesting projects that challenge me to grow.
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 3.6, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                asChild
                size="lg"
                className="h-14 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
              >
                <Link href="/contact" className="flex items-center">
                  <Users className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Get In Touch
                  <ExternalLink className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                asChild
                className="h-14 px-8 border-2 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-300 hover:scale-105 group bg-transparent"
              >
                <Link href="https://github.com/cjjutba" target="_blank" className="flex items-center">
                  <Github className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                  View All Code
                </Link>
              </Button>
            </motion.div>
          </motion.section>
        </motion.div>
      </div>
    </div>
  )
}
