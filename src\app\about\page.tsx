"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MobileNav } from "@/components/mobile-nav"
import {
  GraduationCap,
  Heart,
  Target,
  Users,
  Lightbulb,
  Rocket,
  Coffee,
  BookOpen,
  Code2,
  Palette,
  TrendingUp,
} from "lucide-react"
import { motion, useScroll, useTransform } from "framer-motion"
import { useRef } from "react"

export default function About() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
    },
  }

  const personalInterests = [
    "UI/UX Design",
    "Photography",
    "Tech Podcasts",
    "Gaming",
    "Fitness",
    "Reading",
    "Travel",
    "Music Production",
  ]

  const coreValues = [
    {
      icon: Code2,
      title: "Quality over Quantity",
      description: "I believe in writing clean, maintainable code that stands the test of time",
    },
    {
      icon: Users,
      title: "User-Centric Thinking",
      description: "Every decision should benefit the end user and improve their experience",
    },
    {
      icon: TrendingUp,
      title: "Continuous Improvement",
      description: "Always learning, growing, and refining my skills and approaches",
    },
    {
      icon: Heart,
      title: "Collaboration",
      description: "Great products are built by great teams working together",
    },
  ]

  return (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Animated background elements - Purple Tech Theme */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-tech-purple-400/10 to-tech-purple-600/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], ["0%", "-30%"]) }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-tech-purple-600/10 to-tech-purple-400/10 rounded-full blur-3xl"
        />
      </div>

      <div className="relative p-6 lg:p-8">
        <div className="flex items-center justify-between mb-8 lg:hidden">
          <h1 className="text-2xl font-bold">About</h1>
          <MobileNav />
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-6xl mx-auto space-y-20"
        >
          {/* Enhanced Header */}
          <motion.section variants={itemVariants} className="text-center space-y-8 py-12">
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="relative inline-block"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-tech-purple-600/20 to-tech-purple-400/20 blur-2xl rounded-full" />
                <div className="relative px-6 py-3 rounded-full border border-tech-purple-200/20 bg-white/5 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <BookOpen className="w-4 h-4 text-tech-purple-600 dark:text-tech-purple-400" />
                    <span className="text-sm font-medium text-tech-purple-600 dark:text-tech-purple-400">
                      My Journey & Philosophy
                    </span>
                  </div>
                </div>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.2 }}
                className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
              >
                About
                <br />
                <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                  Me
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              >
                From Computer Engineering to Frontend Development —
                <span className="font-semibold text-foreground"> My Journey of Discovery</span>
              </motion.p>
            </div>
          </motion.section>

          {/* Personal Story */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-blue-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto space-y-8">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center">
                      <GraduationCap className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold">My Story</h2>
                      <p className="text-muted-foreground">
                        The journey from engineering student to frontend developer
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <p className="text-muted-foreground leading-relaxed">
                        I graduated with a degree in Computer Engineering on May 30, 2024, but my journey into frontend
                        development began much earlier. During my studies, I was fascinated by the intersection of
                        technology and user experience.
                      </p>
                      <p className="text-muted-foreground leading-relaxed">
                        While my engineering background gave me a solid foundation in problem-solving and systems
                        thinking, I found my true passion in creating beautiful, functional web applications.
                      </p>
                    </div>
                    <div className="space-y-6">
                      <p className="text-muted-foreground leading-relaxed">
                        The transition from hardware-focused engineering to web development wasn't just a career
                        change—it was a discovery of where my interests and skills naturally aligned.
                      </p>
                      <p className="text-muted-foreground leading-relaxed">
                        I love the immediate feedback loop of frontend development, where you can see your code come to
                        life in real-time and directly impact user experiences.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* What Excites Me */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">What Drives My Passion</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                The elements of coding that fuel my enthusiasm and creativity
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  icon: Lightbulb,
                  title: "Problem Solving",
                  description:
                    "Every bug is a puzzle waiting to be solved, and every feature is an opportunity to think creatively.",
                  color: "from-tech-purple-500 to-tech-purple-600",
                },
                {
                  icon: BookOpen,
                  title: "Continuous Learning",
                  description:
                    "The web development landscape is constantly evolving, which means there's always something new to master.",
                  color: "from-tech-purple-600 to-tech-purple-700",
                },
                {
                  icon: Users,
                  title: "User Impact",
                  description:
                    "Creating interfaces that make people's lives easier and more enjoyable is incredibly rewarding.",
                  color: "from-tech-purple-400 to-tech-purple-500",
                },
                {
                  icon: Palette,
                  title: "Creative Expression",
                  description:
                    "Combining technical skills with design principles to create something both functional and beautiful.",
                  color: "from-tech-purple-500 to-tech-purple-400",
                },
              ].map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="group"
                >
                  <Card className="p-6 border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-tech-purple-900/20 dark:to-tech-purple-800/10 backdrop-blur-sm hover:shadow-xl hover:shadow-tech-purple-500/20 transition-all duration-500 h-full">
                    <div className="space-y-4">
                      <div
                        className={`w-12 h-12 bg-gradient-to-br ${item.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                      >
                        <item.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold group-hover:text-tech-purple-600 transition-colors mb-2">
                          {item.title}
                        </h3>
                        <p className="text-muted-foreground leading-relaxed">{item.description}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Core Values */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-tech-purple-50/80 to-tech-purple-100/60 dark:from-tech-purple-950/30 dark:to-tech-purple-900/20 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-tech-purple-600/5 to-tech-purple-500/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto space-y-8">
                  <div className="text-center space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-tech-purple-100 dark:bg-tech-purple-900/30 text-tech-purple-700 dark:text-tech-purple-300">
                      <Heart className="w-4 h-4" />
                      <span className="text-sm font-medium">Professional Values</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">
                      <span className="bg-gradient-to-r from-tech-purple-600 via-tech-purple-500 to-tech-purple-400 bg-clip-text text-transparent">
                        My Work Philosophy
                      </span>
                    </h2>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                      The principles that guide my approach to development and collaboration
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {coreValues.map((value, index) => (
                      <motion.div
                        key={value.title}
                        initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 1.8 + index * 0.1, duration: 0.6 }}
                        whileHover={{ scale: 1.02 }}
                        className="p-6 rounded-xl bg-white/60 dark:bg-tech-purple-900/20 backdrop-blur-sm border border-white/20 dark:border-tech-purple-500/20 hover:shadow-lg hover:shadow-tech-purple-500/10 transition-all duration-300"
                      >
                        <div className="flex items-start space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-tech-purple-600 to-tech-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <value.icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg mb-2">{value.title}</h3>
                            <p className="text-sm text-muted-foreground leading-relaxed">{value.description}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* Career Goals */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">Future Aspirations</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                My roadmap for growth and the impact I want to make
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 2.2, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card className="p-8 border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-xl transition-all duration-500 h-full">
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <Target className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold">Short-term Goals</h3>
                    </div>
                    <p className="text-muted-foreground mb-4">Next 1-2 years</p>
                    <ul className="space-y-3">
                      {[
                        "Secure a frontend developer position at a growth-stage company",
                        "Master advanced React patterns and state management",
                        "Contribute regularly to open-source projects",
                        "Build a strong professional network in the tech community",
                      ].map((goal, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{goal}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 2.4, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card className="p-8 border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-xl transition-all duration-500 h-full">
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <Rocket className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold">Long-term Vision</h3>
                    </div>
                    <p className="text-muted-foreground mb-4">3-5 years</p>
                    <ul className="space-y-3">
                      {[
                        "Become a senior frontend developer with full-stack capabilities",
                        "Lead development teams and mentor junior developers",
                        "Speak at conferences and share knowledge with the community",
                        "Launch my own SaaS product or consultancy",
                      ].map((goal, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{goal}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </Card>
              </motion.div>
            </div>
          </motion.section>

          {/* Personal Interests */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/30 dark:to-pink-950/30 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto text-center space-y-8">
                  <div className="space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                      <Coffee className="w-4 h-4" />
                      <span className="text-sm font-medium">Beyond Coding</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">Personal Interests</h2>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                      What I enjoy outside of coding that keeps me inspired and balanced
                    </p>
                  </div>

                  <div className="flex flex-wrap justify-center gap-3">
                    {personalInterests.map((interest, index) => (
                      <motion.div
                        key={interest}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 2.6 + index * 0.1, duration: 0.4 }}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Badge
                          variant="secondary"
                          className="px-6 py-3 text-sm font-medium bg-white/60 dark:bg-gray-900/60 hover:bg-white/80 dark:hover:bg-gray-900/80 border-purple-200/20 text-purple-700 dark:text-purple-300 transition-all duration-300 hover:shadow-md cursor-default"
                        >
                          {interest}
                        </Badge>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>
        </motion.div>
      </div>
    </div>
  )
}
