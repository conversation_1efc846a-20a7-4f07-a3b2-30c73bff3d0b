@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode - Purple Tech Theme */
    --background: 0 0% 100%;
    --foreground: 240 10% 9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 9%;
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 98%;
    --secondary-foreground: 240 6% 44%;
    --muted: 0 0% 98%;
    --muted-foreground: 240 4% 44%;
    --accent: 0 0% 98%;
    --accent-foreground: 240 10% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;
    --chart-1: 262 83% 58%;
    --chart-2: 158 64% 52%;
    --chart-3: 43 96% 56%;
    --chart-4: 262 73% 67%;
    --chart-5: 271 91% 65%;
    /* Success/Available color */
    --success: 158 64% 52%;
    --success-foreground: 0 0% 98%;
    /* Warning color */
    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 98%;
  }

  .dark {
    /* Dark Mode - Purple Tech Theme */
    --background: 240 21% 9%;
    --foreground: 210 40% 98%;
    --card: 240 16% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 240 16% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 240 21% 9%;
    --secondary: 240 16% 11%;
    --secondary-foreground: 210 40% 98%;
    --muted: 240 16% 11%;
    --muted-foreground: 240 5% 65%;
    --accent: 240 16% 11%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 16% 11%;
    --input: 240 16% 11%;
    --ring: 262 83% 58%;
    --chart-1: 262 83% 58%;
    --chart-2: 158 64% 52%;
    --chart-3: 43 96% 56%;
    --chart-4: 262 73% 67%;
    --chart-5: 271 91% 65%;
    /* Success/Available color */
    --success: 158 64% 52%;
    --success-foreground: 210 40% 98%;
    /* Warning color */
    --warning: 43 96% 56%;
    --warning-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  code {
    font-family: var(--font-jetbrains-mono), "Fira Code", monospace;
  }
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted/30;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/20 rounded-full;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/40;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-primary/50 ring-offset-2 ring-offset-background;
}

/* Professional animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Glass morphism effects - Purple Tech Theme */
.glass {
  background: rgba(139, 92, 246, 0.1);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.dark .glass {
  background: rgba(139, 92, 246, 0.15);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

/* Professional hover effects - Purple Tech Theme */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
}

.dark .hover-lift:hover {
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.25);
}

/* Text selection */
::selection {
  @apply bg-primary/20 text-primary;
}

/* Gradient text - Purple Tech Theme */
.gradient-text {
  background: linear-gradient(90deg, #7C3AED, #8B5CF6, #A78BFA);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Professional loading states - Purple Tech Theme */
.skeleton {
  background: linear-gradient(90deg, #f8fafc 25%, #e2e8f0 50%, #f8fafc 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.1) 25%, rgba(139, 92, 246, 0.2) 50%, rgba(139, 92, 246, 0.1) 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Professional button effects */
.btn-professional {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-professional::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.btn-professional:hover::before {
  left: 100%;
}

/* Enhanced card styles */
.card-professional {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.card-professional:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Typography enhancements */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}
