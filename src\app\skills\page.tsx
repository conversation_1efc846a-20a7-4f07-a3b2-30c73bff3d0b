"use client"

import { Card, CardContent } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { MobileNav } from "@/src/components/mobile-nav"
import { Code, Palette, Settings, Zap, TrendingUp, Award, Lightbulb, Target, BookOpen, Users } from "lucide-react"
import { motion, useScroll, useTransform } from "framer-motion"
import { useRef } from "react"

export default function Skills() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
    },
  }

  const technicalSkills = [
    { name: "HTML/CSS", level: 95, category: "Frontend", color: "from-orange-500 to-red-600", icon: Code },
    { name: "JavaScript", level: 90, category: "Frontend", color: "from-yellow-500 to-orange-600", icon: Code },
    { name: "React", level: 88, category: "Frontend", color: "from-blue-500 to-cyan-600", icon: Code },
    { name: "Next.js", level: 85, category: "Frontend", color: "from-gray-700 to-gray-900", icon: Code },
    { name: "TypeScript", level: 82, category: "Frontend", color: "from-blue-600 to-blue-800", icon: Code },
    { name: "Tailwind CSS", level: 92, category: "Styling", color: "from-cyan-500 to-blue-600", icon: Palette },
    { name: "SCSS/Sass", level: 80, category: "Styling", color: "from-pink-500 to-rose-600", icon: Palette },
    { name: "Figma", level: 75, category: "Design", color: "from-purple-500 to-pink-600", icon: Palette },
    { name: "Git", level: 85, category: "Tools", color: "from-orange-600 to-red-700", icon: Settings },
    { name: "VS Code", level: 95, category: "Tools", color: "from-blue-600 to-indigo-700", icon: Settings },
    { name: "Node.js", level: 70, category: "Backend", color: "from-green-500 to-emerald-600", icon: Settings },
    { name: "API Integration", level: 78, category: "Backend", color: "from-indigo-500 to-purple-600", icon: Zap },
  ]

  const softSkills = [
    "Problem Solving",
    "Communication",
    "Team Collaboration",
    "Time Management",
    "Attention to Detail",
    "Adaptability",
    "Critical Thinking",
    "Project Management",
  ]

  const currentlyLearning = [
    { name: "Node.js & Express", progress: 70, color: "from-green-500 to-emerald-600" },
    { name: "PostgreSQL", progress: 60, color: "from-blue-600 to-indigo-700" },
    { name: "Docker", progress: 45, color: "from-blue-500 to-cyan-600" },
    { name: "AWS Fundamentals", progress: 40, color: "from-orange-500 to-yellow-600" },
    { name: "GraphQL", progress: 55, color: "from-pink-500 to-rose-600" },
    { name: "Testing (Jest, Cypress)", progress: 50, color: "from-purple-500 to-indigo-600" },
  ]

  const groupedSkills = technicalSkills.reduce(
    (acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = []
      }
      acc[skill.category].push(skill)
      return acc
    },
    {} as Record<string, typeof technicalSkills>,
  )

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Frontend":
        return <Code className="h-6 w-6 text-white" />
      case "Styling":
        return <Palette className="h-6 w-6 text-white" />
      case "Design":
        return <Palette className="h-6 w-6 text-white" />
      case "Tools":
        return <Settings className="h-6 w-6 text-white" />
      case "Backend":
        return <Zap className="h-6 w-6 text-white" />
      default:
        return <Code className="h-6 w-6 text-white" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "from-blue-500 to-cyan-600"
      case "Styling":
        return "from-purple-500 to-pink-600"
      case "Design":
        return "from-pink-500 to-rose-600"
      case "Tools":
        return "from-gray-600 to-gray-800"
      case "Backend":
        return "from-green-500 to-emerald-600"
      default:
        return "from-blue-500 to-purple-600"
    }
  }

  return (
    <div ref={containerRef} className="min-h-screen relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          style={{ y, opacity }}
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: useTransform(scrollYProgress, [0, 1], ["0%", "-30%"]) }}
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-emerald-400/10 to-blue-400/10 rounded-full blur-3xl"
        />
      </div>

      <div className="relative p-6 lg:p-8">
        <div className="flex items-center justify-between mb-8 lg:hidden">
          <h1 className="text-2xl font-bold">Skills</h1>
          <MobileNav />
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-6xl mx-auto space-y-20"
        >
          {/* Enhanced Header */}
          <motion.section variants={itemVariants} className="text-center space-y-8 py-12">
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="relative inline-block"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-2xl rounded-full" />
                <div className="relative px-6 py-3 rounded-full border border-blue-200/20 bg-white/5 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <Award className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      Technical Expertise & Capabilities
                    </span>
                  </div>
                </div>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.2 }}
                className="text-6xl lg:text-8xl font-bold tracking-tight leading-none"
              >
                My
                <br />
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent">
                  Skills
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              >
                Technical expertise and soft skills developed through
                <span className="font-semibold text-foreground"> hands-on projects</span> and
                <span className="font-semibold text-foreground"> continuous learning</span>
              </motion.p>
            </div>
          </motion.section>

          {/* Technical Skills */}
          <motion.section variants={itemVariants} className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold">Technical Proficiency</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Core technologies and tools I use to build modern web applications
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {Object.entries(groupedSkills).map(([category, skills], categoryIndex) => (
                <motion.div
                  key={category}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 + categoryIndex * 0.1, duration: 0.6 }}
                  whileHover={{ scale: 1.02 }}
                  className="group"
                >
                  <Card className="p-8 border-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-xl transition-all duration-500">
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-12 h-12 bg-gradient-to-br ${getCategoryColor(category)} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                        >
                          {getCategoryIcon(category)}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold group-hover:text-blue-600 transition-colors">{category}</h3>
                          <p className="text-sm text-muted-foreground">
                            {skills.length} {skills.length === 1 ? "skill" : "skills"}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        {skills.map((skill, skillIndex) => (
                          <div key={skill.name} className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="font-medium text-sm">{skill.name}</span>
                              <span className="text-sm font-bold text-blue-600">{skill.level}%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                              <motion.div
                                initial={{ width: 0 }}
                                animate={{ width: `${skill.level}%` }}
                                transition={{
                                  delay: 1.5 + categoryIndex * 0.1 + skillIndex * 0.05,
                                  duration: 1.2,
                                  ease: "easeOut",
                                }}
                                className={`h-full bg-gradient-to-r ${skill.color} rounded-full relative`}
                              >
                                <div className="absolute inset-0 bg-white/20 animate-pulse" />
                              </motion.div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Currently Learning */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50/80 to-purple-50/80 dark:from-blue-950/30 dark:to-purple-950/30 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto space-y-8">
                  <div className="text-center space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                      <TrendingUp className="w-4 h-4" />
                      <span className="text-sm font-medium">Expanding Knowledge</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">Currently Learning</h2>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                      Technologies and concepts I'm actively exploring to expand my skillset
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {currentlyLearning.map((skill, index) => (
                      <motion.div
                        key={skill.name}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 2.2 + index * 0.1, duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        className="p-6 rounded-xl bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300"
                      >
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-semibold text-sm">{skill.name}</h4>
                            <span className="text-xs font-bold text-blue-600">{skill.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${skill.progress}%` }}
                              transition={{ delay: 2.5 + index * 0.1, duration: 1, ease: "easeOut" }}
                              className={`h-full bg-gradient-to-r ${skill.color} rounded-full`}
                            />
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* Soft Skills */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/30 dark:to-pink-950/30 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto text-center space-y-8">
                  <div className="space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                      <Users className="w-4 h-4" />
                      <span className="text-sm font-medium">Professional Skills</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">Soft Skills</h2>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                      Personal and professional skills that complement my technical abilities
                    </p>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {softSkills.map((skill, index) => (
                      <motion.div
                        key={skill}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 2.8 + index * 0.1, duration: 0.4 }}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Badge
                          variant="secondary"
                          className="w-full justify-center py-3 px-4 text-sm font-medium bg-white/60 dark:bg-gray-900/60 hover:bg-white/80 dark:hover:bg-gray-900/80 border-purple-200/20 text-purple-700 dark:text-purple-300 transition-all duration-300 hover:shadow-md cursor-default"
                        >
                          {skill}
                        </Badge>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>

          {/* Learning Philosophy */}
          <motion.section variants={itemVariants}>
            <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50/80 to-blue-50/80 dark:from-emerald-950/30 dark:to-blue-950/30 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/5 to-blue-600/5" />
              <CardContent className="relative p-8 lg:p-12">
                <div className="max-w-4xl mx-auto space-y-8">
                  <div className="text-center space-y-4">
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300">
                      <Lightbulb className="w-4 h-4" />
                      <span className="text-sm font-medium">Growth Mindset</span>
                    </div>
                    <h2 className="text-3xl lg:text-4xl font-bold">My Learning Philosophy</h2>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <p className="text-muted-foreground leading-relaxed text-lg">
                        I believe in continuous learning and staying curious about new technologies. Rather than trying
                        to master everything at once, I focus on building a strong foundation in core technologies while
                        gradually expanding into new areas based on project needs and industry trends.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                      <div className="p-6 rounded-xl bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/20">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <BookOpen className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2">Learning Approach</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              <li>• Build projects to reinforce concepts</li>
                              <li>• Read documentation and best practices</li>
                              <li>• Participate in developer communities</li>
                              <li>• Contribute to open-source projects</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div className="p-6 rounded-xl bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/20">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <Target className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2">Skill Development</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              <li>• Focus on fundamentals first</li>
                              <li>• Practice through real-world projects</li>
                              <li>• Seek feedback from experienced developers</li>
                              <li>• Stay updated with industry trends</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>
        </motion.div>
      </div>
    </div>
  )
}
