"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Code, FolderOpen, Home, Mail, User, Moon, Sun } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import * as React from "react"

const navItems = [
  { href: "/", icon: Home, label: "Dashboard" },
  { href: "/about", icon: User, label: "About" },
  { href: "/skills", icon: Code, label: "Skills" },
  { href: "/projects", icon: FolderOpen, label: "Projects" },
  { href: "/contact", icon: Mail, label: "Contact" },
]

export function FloatingNav() {
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = React.useState(false)
  const navRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const handleDragStart = React.useCallback((event: any, info: any) => {
    setIsDragging(true)
  }, [])

  const handleDragEnd = React.useCallback((event: any, info: any) => {
    setIsDragging(false)
    setPosition({ x: info.point.x, y: info.point.y })
  }, [])

  const handleDrag = React.useCallback((event: any, info: any) => {
    // Prevent default drag behavior
    event.preventDefault()
  }, [])

  // Don't render until mounted to avoid SSR issues
  if (!mounted) {
    return null
  }

  return (
    <TooltipProvider>
      <motion.div
        ref={navRef}
        drag
        dragMomentum={false}
        dragElastic={0}
        dragConstraints={{
          top: -window.innerHeight / 2 + 150,
          left: -window.innerWidth / 2 + 100,
          right: window.innerWidth / 2 - 100,
          bottom: window.innerHeight / 2 - 150,
        }}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        initial={{ opacity: 0, x: 0, y: 0 }}
        animate={{ opacity: 1, x: position.x, y: position.y }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed right-6 top-1/2 -translate-y-1/2 z-[9999] hidden lg:block select-none"
        style={{
          cursor: isDragging ? "grabbing" : "grab",
          pointerEvents: "auto",
          touchAction: "none",
        }}
      >
        <nav className="flex flex-col space-y-2 rounded-xl border border-border/50 bg-background/95 backdrop-blur-sm p-2 shadow-lg">
          {navItems.map((item, index) => {
            const Icon = item.icon
            const isActive = pathname === item.href

            return (
              <Tooltip key={item.href}>
                <TooltipTrigger asChild>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="icon"
                    className={cn(
                      "h-10 w-10 rounded-lg transition-all duration-200 pointer-events-auto",
                      isActive
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "hover:bg-accent hover:text-accent-foreground",
                      isDragging && "pointer-events-none",
                    )}
                    asChild={!isDragging}
                    disabled={isDragging}
                  >
                    {isDragging ? (
                      <div>
                        <Icon className="h-4 w-4" />
                        <span className="sr-only">{item.label}</span>
                      </div>
                    ) : (
                      <Link href={item.href} onDragStart={(e) => e.preventDefault()}>
                        <Icon className="h-4 w-4" />
                        <span className="sr-only">{item.label}</span>
                      </Link>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="bg-background border-border/50">
                  <p className="font-medium">{item.label}</p>
                </TooltipContent>
              </Tooltip>
            )
          })}

          {/* Theme Toggle */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={isDragging ? undefined : () => setTheme(theme === "light" ? "dark" : "light")}
                className={cn(
                  "h-10 w-10 rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-200 pointer-events-auto",
                  isDragging && "pointer-events-none",
                )}
                disabled={isDragging}
                onDragStart={(e) => e.preventDefault()}
              >
                {mounted && (
                  <>
                    <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                    <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                  </>
                )}
                <span className="sr-only">Toggle theme</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-background border-border/50">
              <p className="font-medium">Toggle theme</p>
            </TooltipContent>
          </Tooltip>
        </nav>
      </motion.div>
    </TooltipProvider>
  )
}
