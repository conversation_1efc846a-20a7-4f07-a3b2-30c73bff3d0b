"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Download, Gith<PERSON>, Linkedin, Mail } from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"

export function Sidebar() {
  return (
    <aside className="fixed left-0 top-0 z-40 h-screen w-80 border-r border-border/50 bg-background/95 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 lg:block hidden">
      <div className="relative flex h-full flex-col p-8 overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="flex flex-col items-center space-y-6"
        >
          <div className="relative">
            <Avatar className="relative h-28 w-28 ring-1 ring-border shadow-sm transition-all duration-300">
              <AvatarImage src="/images/profile.png" alt="CJ Jutba" className="object-cover" />
              <AvatarFallback className="text-2xl font-bold bg-primary text-primary-foreground">CJ</AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 rounded-full border-2 border-background">
              <div className="h-full w-full bg-green-400 rounded-full animate-pulse" />
            </div>
          </div>

          <div className="text-center space-y-3">
            <motion.h1
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-3xl font-bold tracking-tight"
            >
              CJ Jutba
            </motion.h1>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="flex items-center justify-center space-x-2"
            >
              <p className="text-lg font-medium text-primary">Frontend Developer</p>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <Card className="mt-8 p-6 bg-card border-border/50 shadow-sm">
            <p className="text-center text-sm leading-relaxed text-muted-foreground font-medium">
              I build modern websites that deliver{" "}
              <span className="text-primary font-semibold">exceptional user experiences</span>
            </p>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mt-8 space-y-4"
        >
          <div className="space-y-4">
            <Link
              href="mailto:<EMAIL>"
              className="group flex items-center space-x-4 p-3 rounded-xl hover:bg-accent transition-colors duration-200"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-500/10">
                <Mail className="h-4 w-4 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">Email</p>
                <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
              </div>
            </Link>

            <Link
              href="https://linkedin.com/in/cjjutba"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center space-x-4 p-3 rounded-xl hover:bg-accent transition-colors duration-200"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500/10">
                <Linkedin className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">LinkedIn</p>
                <p className="text-xs text-muted-foreground truncate">linkedin.com/in/cjjutba</p>
              </div>
            </Link>

            <Link
              href="https://github.com/cjjutba"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center space-x-4 p-3 rounded-xl hover:bg-accent transition-colors duration-200"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-500/10">
                <Github className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">GitHub</p>
                <p className="text-xs text-muted-foreground truncate">github.com/cjjutba</p>
              </div>
            </Link>
          </div>
        </motion.div>

        <div className="mt-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            <Button
              className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold shadow-sm transition-colors duration-200"
              size="lg"
            >
              <Download className="mr-2 h-4 w-4" />
              Download Resume
            </Button>
          </motion.div>
        </div>
      </div>
    </aside>
  )
}
