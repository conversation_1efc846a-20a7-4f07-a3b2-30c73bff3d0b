import { 
  Home, 
  User, 
  Code, 
  FolderOpen, 
  Mail, 
  Github, 
  Linkedin, 
  Twitter,
  ExternalLink
} from "lucide-react"

// Navigation items
export const NAVIGATION_ITEMS = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "About",
    href: "/about",
    icon: User,
  },
  {
    name: "Skills",
    href: "/skills",
    icon: Code,
  },
  {
    name: "Projects",
    href: "/projects",
    icon: FolderOpen,
  },
  {
    name: "Contact",
    href: "/contact",
    icon: Mail,
  },
] as const

// Social links
export const SOCIAL_LINKS = [
  {
    name: "GitHub",
    url: "https://github.com/yourusername",
    icon: Github,
  },
  {
    name: "LinkedIn",
    url: "https://linkedin.com/in/yourusername",
    icon: Linkedin,
  },
  {
    name: "Twitter",
    url: "https://twitter.com/yourusername",
    icon: Twitter,
  },
] as const

// Contact information
export const CONTACT_INFO = {
  email: "<EMAIL>",
  phone: "+****************",
  location: "Your City, Country",
} as const

// Site metadata
export const SITE_CONFIG = {
  name: "Your Name",
  title: "Frontend Developer & UI/UX Enthusiast",
  description: "Computer Engineering graduate specializing in modern web applications with a focus on performance and user experience.",
  url: "https://yourportfolio.com",
  ogImage: "/og-image.jpg",
  keywords: [
    "Frontend Developer",
    "React",
    "Next.js",
    "TypeScript",
    "Tailwind CSS",
    "Web Development",
    "UI/UX",
    "Portfolio",
  ],
} as const

// Animation durations
export const ANIMATION_DURATION = {
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8,
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

// Color palette
export const COLORS = {
  brand: {
    purple: "#8B5CF6", // violet-500
    dark: "#7C3AED",   // violet-600
    light: "#A78BFA",  // violet-400
  },
  background: {
    light: "#0F0F23",
    dark: "#1A1A2E",
  },
} as const
