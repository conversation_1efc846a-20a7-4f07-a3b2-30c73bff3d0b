import { 
  Lightbulb, 
  BookOpen, 
  Users, 
  Palette,
  Target,
  Zap,
  Heart,
  Shield,
  Award,
  TrendingUp
} from "lucide-react"
import type { Skill, Project, Achievement, CoreValue } from "@/types"

// Skills data
export const skills: Skill[] = [
  {
    name: "React & Next.js",
    level: 90,
    category: "Frontend Framework",
    color: "from-tech-purple-600 to-tech-purple-500",
  },
  {
    name: "TypeScript",
    level: 85,
    category: "Programming Language",
    color: "from-tech-purple-600 to-tech-purple-500",
  },
  {
    name: "Tailwind CSS",
    level: 95,
    category: "Styling",
    color: "from-tech-purple-600 to-tech-purple-500",
  },
  {
    name: "JavaScript (ES6+)",
    level: 88,
    category: "Programming Language",
    color: "from-tech-purple-600 to-tech-purple-500",
  },
]

// Projects data
export const projects: Project[] = [
  {
    id: "1",
    title: "E-Commerce Platform",
    description: "A modern e-commerce platform built with Next.js and Stripe integration",
    longDescription: "A full-featured e-commerce platform with user authentication, product management, shopping cart, and secure payment processing using Stripe. Built with Next.js 14, TypeScript, and Tailwind CSS.",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Stripe", "Prisma"],
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    liveUrl: "https://your-ecommerce-demo.vercel.app",
    imageUrl: "/projects/ecommerce.jpg",
    featured: true,
    status: "completed",
  },
  {
    id: "2",
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates",
    longDescription: "A comprehensive task management solution with team collaboration features, real-time updates, drag-and-drop functionality, and progress tracking.",
    technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Express"],
    githubUrl: "https://github.com/yourusername/task-manager",
    liveUrl: "https://your-task-manager.vercel.app",
    imageUrl: "/projects/task-manager.jpg",
    featured: true,
    status: "completed",
  },
]

// Achievements data
export const achievements: Achievement[] = [
  {
    icon: Target,
    label: "Problem Solver",
    description: "Analytical approach to breaking down complex challenges into manageable solutions",
  },
  {
    icon: Zap,
    label: "Fast Learner",
    description: "Quick to adapt and master new technologies and development methodologies",
  },
  {
    icon: Users,
    label: "Team Player",
    description: "Collaborative mindset with strong communication and interpersonal skills",
  },
  {
    icon: Award,
    label: "Quality Focused",
    description: "Committed to writing clean, maintainable code and following best practices",
  },
]

// Core values data
export const coreValues: CoreValue[] = [
  {
    icon: Lightbulb,
    title: "Innovation",
    description: "Always exploring new technologies and creative solutions to enhance user experiences.",
  },
  {
    icon: BookOpen,
    title: "Continuous Learning",
    description: "Committed to staying current with industry trends and expanding technical knowledge.",
  },
  {
    icon: Users,
    title: "User-Centric",
    description: "Prioritizing user needs and accessibility in every design and development decision.",
  },
  {
    icon: Shield,
    title: "Quality Assurance",
    description: "Maintaining high standards through testing, code reviews, and best practices.",
  },
]

// Passion areas data
export const passionAreas = [
  {
    icon: Lightbulb,
    title: "Problem Solving",
    description: "Every bug is a puzzle waiting to be solved, and every feature is an opportunity to think creatively.",
    color: "from-tech-purple-500 to-tech-purple-600",
  },
  {
    icon: BookOpen,
    title: "Continuous Learning",
    description: "The web development landscape is constantly evolving, which means there's always something new to master.",
    color: "from-tech-purple-600 to-tech-purple-700",
  },
  {
    icon: Users,
    title: "User Impact",
    description: "Creating interfaces that make people's lives easier and more enjoyable is incredibly rewarding.",
    color: "from-tech-purple-400 to-tech-purple-500",
  },
  {
    icon: Palette,
    title: "Creative Expression",
    description: "Combining technical skills with design principles to create something both functional and beautiful.",
    color: "from-tech-purple-500 to-tech-purple-400",
  },
]
