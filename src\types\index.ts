// Global type definitions for the portfolio website

export interface Skill {
  name: string
  level: number
  category: string
  color?: string
}

export interface Project {
  id: string
  title: string
  description: string
  longDescription?: string
  technologies: string[]
  githubUrl?: string
  liveUrl?: string
  imageUrl?: string
  featured?: boolean
  status: 'completed' | 'in-progress' | 'planned'
}

export interface Experience {
  id: string
  company: string
  position: string
  duration: string
  description: string
  technologies: string[]
  achievements?: string[]
}

export interface Education {
  id: string
  institution: string
  degree: string
  field: string
  duration: string
  gpa?: string
  achievements?: string[]
}

export interface ContactForm {
  name: string
  email: string
  subject: string
  message: string
}

export interface Achievement {
  icon: React.ComponentType<{ className?: string }>
  label: string
  description: string
}

export interface CoreValue {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
}

export interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

export interface SocialLink {
  name: string
  url: string
  icon: React.ComponentType<{ className?: string }>
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

// Animation variants
export interface AnimationVariants {
  hidden: {
    opacity: number
    y?: number
    x?: number
    scale?: number
  }
  visible: {
    opacity: number
    y?: number
    x?: number
    scale?: number
    transition?: {
      duration?: number
      delay?: number
      ease?: string
      staggerChildren?: number
    }
  }
}
